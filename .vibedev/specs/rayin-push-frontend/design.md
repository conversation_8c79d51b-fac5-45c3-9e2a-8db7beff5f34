# Rayin-Push Frontend Design Document

## 概述

Rayin-Push 前端是一个基于 Next.js 14 App Router 的现代化 webhook 转发管理系统界面。系统采用模块化架构设计，支持国际化和响应式布局，为用户提供完整的多渠道消息推送管理功能。

### 核心设计原则
- **组件化架构**: 使用可复用的 React 组件构建
- **类型安全**: 全面使用 TypeScript 确保代码质量
- **响应式设计**: 适配桌面、平板、移动设备
- **可访问性**: 遵循 WCAG 2.1 标准
- **性能优化**: 代码分割和懒加载
- **国际化支持**: 支持中英文切换

## 技术架构

### 技术栈选择

```mermaid
graph TB
    A[Next.js 14 App Router] --> B[React 18]
    A --> C[TypeScript]
    B --> D[Shadcn/ui]
    B --> E[Zustand]
    B --> F[React-i18next]
    C --> G[TailwindCSS]
    C --> H[Radix UI]
    E --> I[Framer Motion]
    
    J[开发工具] --> K[ESLint]
    J --> L[Prettier]
    J --> M[pnpm]
```

**技术选择理由**:
- **Next.js 14**: 最新的 App Router 提供更好的开发体验和性能
- **Shadcn/ui**: 基于 Radix UI 的现代化组件库，可定制性强
- **Zustand**: 轻量级状态管理，避免 Redux 的复杂性
- **i18next**: 成熟的国际化解决方案，支持 App Router
- **TailwindCSS**: 实用程序优先的 CSS 框架，提高开发效率

### 项目目录结构

```
rayin-push-frontend/
├── public/                      # 静态资源
│   ├── images/                  # 图片资源
│   ├── icons/                   # 图标文件
│   └── locales/                 # 国际化资源文件
│       ├── zh/                  # 中文翻译
│       │   ├── common.json
│       │   ├── dashboard.json
│       │   ├── config.json
│       │   ├── channels.json
│       │   ├── logs.json
│       │   ├── limits.json
│       │   └── users.json
│       └── en/                  # 英文翻译
│           └── ...
├── src/
│   ├── app/                     # Next.js 14 App Router
│   │   ├── [locale]/            # 国际化路由
│   │   │   ├── dashboard/       # 仪表盘
│   │   │   │   └── page.tsx
│   │   │   ├── config/          # 接口配置管理
│   │   │   │   ├── page.tsx
│   │   │   │   └── [id]/
│   │   │   │       └── page.tsx
│   │   │   ├── channels/        # 通知渠道
│   │   │   │   ├── page.tsx
│   │   │   │   └── [id]/
│   │   │   │       └── page.tsx
│   │   │   ├── logs/            # 请求日志
│   │   │   │   ├── page.tsx
│   │   │   │   └── [id]/
│   │   │   │       └── page.tsx
│   │   │   ├── limits/          # 请求限制
│   │   │   │   └── page.tsx
│   │   │   ├── users/           # 用户管理
│   │   │   │   └── page.tsx
│   │   │   └── layout.tsx       # 主布局
│   │   ├── globals.css          # 全局样式
│   │   ├── layout.tsx           # 根布局
│   │   └── not-found.tsx        # 404 页面
│   ├── components/              # 组件目录
│   │   ├── ui/                  # Shadcn UI 组件
│   │   │   ├── button.tsx
│   │   │   ├── card.tsx
│   │   │   ├── table.tsx
│   │   │   ├── dialog.tsx
│   │   │   ├── form.tsx
│   │   │   └── ...
│   │   ├── layout/              # 布局组件
│   │   │   ├── sidebar.tsx
│   │   │   ├── header.tsx
│   │   │   ├── breadcrumb.tsx
│   │   │   └── mobile-nav.tsx
│   │   ├── dashboard/           # 仪表盘组件
│   │   │   ├── stats-card.tsx
│   │   │   ├── recent-requests.tsx
│   │   │   ├── trend-chart.tsx
│   │   │   └── refresh-button.tsx
│   │   ├── config/              # 配置管理组件
│   │   │   ├── config-list.tsx
│   │   │   ├── config-form.tsx
│   │   │   ├── config-toolbar.tsx
│   │   │   └── parsing-rules.tsx
│   │   ├── channels/            # 通知渠道组件
│   │   │   ├── channel-list.tsx
│   │   │   ├── channel-form.tsx
│   │   │   ├── template-selector.tsx
│   │   │   └── variable-editor.tsx
│   │   ├── logs/                # 日志组件
│   │   │   ├── log-list.tsx
│   │   │   ├── log-detail.tsx
│   │   │   ├── log-filters.tsx
│   │   │   └── export-button.tsx
│   │   ├── limits/              # 限制管理组件
│   │   │   ├── rate-limit-form.tsx
│   │   │   ├── ip-limit-form.tsx
│   │   │   └── limit-stats.tsx
│   │   ├── users/               # 用户管理组件
│   │   │   ├── user-list.tsx
│   │   │   ├── user-form.tsx
│   │   │   └── role-selector.tsx
│   │   └── common/              # 通用组件
│   │       ├── data-table.tsx
│   │       ├── search-input.tsx
│   │       ├── language-switcher.tsx
│   │       ├── theme-switcher.tsx
│   │       ├── loading-spinner.tsx
│   │       └── error-boundary.tsx
│   ├── lib/                     # 工具库
│   │   ├── utils.ts             # 通用工具函数
│   │   ├── validations.ts       # 表单验证
│   │   ├── constants.ts         # 常量定义
│   │   ├── formatters.ts        # 数据格式化
│   │   └── api.ts               # API 工具函数
│   ├── store/                   # 状态管理
│   │   ├── dashboard.ts         # 仪表盘状态
│   │   ├── config.ts            # 配置管理状态
│   │   ├── channels.ts          # 通知渠道状态
│   │   ├── logs.ts              # 日志状态
│   │   ├── limits.ts            # 限制管理状态
│   │   ├── users.ts             # 用户管理状态
│   │   ├── auth.ts              # 认证状态
│   │   └── global.ts            # 全局状态
│   ├── hooks/                   # 自定义 Hooks
│   │   ├── use-translation.ts   # 国际化 Hook
│   │   ├── use-theme.ts         # 主题 Hook
│   │   ├── use-mobile.ts        # 移动端检测
│   │   └── use-debounce.ts      # 防抖 Hook
│   ├── types/                   # TypeScript 类型定义
│   │   ├── api.ts               # API 相关类型
│   │   ├── store.ts             # Store 类型
│   │   ├── components.ts        # 组件 Props 类型
│   │   └── i18n.ts              # 国际化类型
│   └── mock/                    # 模拟数据
│       ├── dashboard.ts         # 仪表盘模拟数据
│       ├── config.ts            # 配置模拟数据
│       ├── channels.ts          # 渠道模拟数据
│       ├── logs.ts              # 日志模拟数据
│       ├── limits.ts            # 限制模拟数据
│       └── users.ts             # 用户模拟数据
├── components.json              # Shadcn 配置
├── next.config.js               # Next.js 配置
├── tailwind.config.js           # TailwindCSS 配置
├── tsconfig.json                # TypeScript 配置
├── package.json                 # 依赖配置
└── README.md                    # 项目文档
```

## 组件和接口设计

### 核心组件架构

```mermaid
graph TD
    A[App Layout] --> B[Sidebar Navigation]
    A --> C[Header]
    A --> D[Main Content]
    
    B --> E[Menu Items]
    B --> F[Language Switcher]
    B --> G[Theme Toggle]
    
    C --> H[Breadcrumb]
    C --> I[User Menu]
    C --> J[Mobile Nav Toggle]
    
    D --> K[Dashboard]
    D --> L[Config Management]
    D --> M[Notification Channels]
    D --> N[Request Logs]
    D --> O[Rate Limits]
    D --> P[User Management]
```

### 主要组件接口

#### 1. 布局组件

```typescript
// components/layout/sidebar.tsx
interface SidebarProps {
  isCollapsed: boolean;
  onToggle: () => void;
  currentPath: string;
}

// components/layout/header.tsx
interface HeaderProps {
  title: string;
  breadcrumbs: BreadcrumbItem[];
  user: UserProfile;
}

interface BreadcrumbItem {
  label: string;
  href?: string;
}
```

#### 2. 仪表盘组件

```typescript
// components/dashboard/stats-card.tsx
interface StatsCardProps {
  title: string;
  value: string | number;
  change?: {
    value: number;
    type: 'increase' | 'decrease';
  };
  icon: React.ReactNode;
  loading?: boolean;
  type: 'today-requests' | 'total-requests' | 'success-rate';
}

// components/dashboard/trend-chart.tsx
interface TrendChartProps {
  data: ChartDataPoint[];
  timeRange: '24h' | '7d' | '30d';
  onTimeRangeChange: (range: string) => void;
  loading?: boolean;
  showSuccessRate?: boolean;
}

interface ChartDataPoint {
  timestamp: number;
  requests: number;
  success: number;
  failed: number;
  successRate: number;
}

// components/dashboard/recent-requests.tsx
interface RecentRequestsProps {
  requests: RecentRequest[];
  loading?: boolean;
  onRequestClick: (requestId: string) => void;
}

interface RecentRequest {
  id: string;
  interfaceName: string;
  status: 'success' | 'failed' | 'partial';
  requestTime: string;
  duration?: number;
}
```

#### 3. 配置管理组件

```typescript
// components/config/config-form.tsx
interface ConfigFormProps {
  config?: WebhookConfig;
  onSubmit: (data: WebhookConfigFormData) => void;
  onCancel: () => void;
  loading?: boolean;
}

interface WebhookConfigFormData {
  na/me: string;
  description: string;
  requestMethod: 'GET' | 'POST';
  channels: string[];
  parsingRules: ParsingRule[];
  status: 'active' | 'inactive';
}

interface WebhookConfig {
  id: string;
  name: string;
  token: string;
  description: string; 
  requestMethod: 'GET' | 'POST';
  status: 'active' | 'inactive';
  channels: string[];
  parsingRules: ParsingRule[];
  createdTime: string;
  updatedTime: string;
}

// components/config/parsing-rules.tsx
interface ParsingRulesProps {
  rules: ParsingRule[];
  onRulesChange: (rules: ParsingRule[]) => void;
  requestMethod: 'GET' | 'POST';
}

interface ParsingRule {
  type: 'get-params' | 'post-form' | 'post-multipart' | 'post-json' | 'post-plain';
  mappings: FieldMapping[];
  regexPatterns?: RegexPattern[];
}

interface FieldMapping {
  sourceField: string;
  targetField: string;
  fieldPath?: string; // For nested JSON fields like "info.address"
  required: boolean;
  defaultValue?: string;
}

interface RegexPattern {
  pattern: string;
  flags: string;
  captureGroups: string[];
}
```

#### 4. 通知渠道组件

```typescript
// components/channels/channel-form.tsx
interface ChannelFormProps {
  channel?: NotificationChannel;
  onSubmit: (data: ChannelFormData) => void;
  onCancel: () => void;
  loading?: boolean;
}

interface ChannelFormData {
  name: string;
  type: 'wechat' | 'feishu' | 'webhook';
  config: ChannelConfig;
  template: string;
  variables: TemplateVariable[];
  status: 'active' | 'inactive';
}

interface NotificationChannel {
  id: string;
  name: string;
  type: 'wechat' | 'feishu' | 'webhook';
  config: ChannelConfig;
  template: string;
  variables: TemplateVariable[];
  status: 'active' | 'inactive';
  requestMethod: 'GET' | 'POST';
  createdTime: string;
  updatedTime: string;
}

interface ChannelConfig {
  url: string;
  method: 'GET' | 'POST';
  headers: Record<string, string>;
  contentType: 'application/x-www-form-urlencoded' | 'multipart/form-data' | 'application/json' | 'text/plain';
  body?: string;
  queryParams?: Record<string, string>; // For GET requests
}

// components/channels/template-selector.tsx
interface TemplateSelectorProps {
  selectedTemplate: 'wechat' | 'feishu' | 'custom' | null;
  onTemplateSelect: (template: string) => void;
}

// components/channels/variable-editor.tsx
interface VariableEditorProps {
  template: string;
  variables: TemplateVariable[];
  onTemplateChange: (template: string) => void;
  onVariablesChange: (variables: TemplateVariable[]) => void;
  availableVariables: string[]; // Variables extracted from webhook config
}

interface TemplateVariable {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'expression';
  source: 'extracted' | 'builtin' | 'computed'; // extracted: ${xxx}, builtin: ${data}/${now}, computed: ${x1 + x2}
  expression?: string; // For computed variables and if/else statements
  required: boolean;
  defaultValue?: any;
}
```

## 数据模型

### 核心数据类型

```typescript
// types/api.ts

// 用户相关
interface User {
  id: string;
  username: string;
  email: string;
  role: 'admin' | 'user';
  status: 'active' | 'inactive';
  createdTime: string;
  lastLoginAt?: string;
}

// 接口配置
interface WebhookConfig {
  id: string;
  name: string;
  token: string;
  description: string;
  requestMethod: 'GET' | 'POST';
  status: 'active' | 'inactive';
  channels: string[];
  parsingRules: ParsingRule[];
  stats: {
    totalRequests: number;
    successRate: number;
    lastRequestAt?: string;
  };
  createdTime: string;
  updatedTime: string;
}

interface ParsingRule {
  id: string;
  type: 'get-params' | 'post-form' | 'post-multipart' | 'post-json' | 'post-plain';
  mappings: FieldMapping[];
  regexPatterns?: RegexPattern[];
}

interface FieldMapping {
  sourceField: string;
  targetField: string;
  fieldPath?: string; // For nested JSON fields like "info.address"
  required: boolean;
  defaultValue?: string;
}

interface RegexPattern {
  pattern: string;
  flags: string;
  captureGroups: string[];
}

// 通知渠道
interface NotificationChannel {
  id: string;
  name: string;
  type: 'wechat' | 'feishu' | 'webhook';
  config: {
    url: string;
    method: 'GET' | 'POST';
    headers: Record<string, string>;
    contentType: 'application/x-www-form-urlencoded' | 'multipart/form-data' | 'application/json' | 'text/plain';
    body?: string;
    queryParams?: Record<string, string>;
  };
  template: string;
  variables: TemplateVariable[];
  status: 'active' | 'inactive';
  stats: {
    totalSent: number;
    successRate: number;
    lastUsedAt?: string;
  };
  createdTime: string;
  updatedTime: string;
}

interface TemplateVariable {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'expression';
  source: 'extracted' | 'builtin' | 'computed';
  expression?: string;
  required: boolean;
  defaultValue?: any;
}

// 请求日志
interface RequestLog {
  id: string;
  configId: string;
  configName: string;
  status: 'success' | 'failed' | 'partial';
  duration: number;
  requestTime: string;
  error?: string;
  originalData: any;
  processedData: any;
  channelResults: ChannelResult[];
}

interface ChannelResult {
  channelId: string;
  channelName: string;
  status: 'success' | 'failed';
  response?: any;
  error?: string;
  duration: number;
}

// 限制规则
interface RateLimit {
  id: string;
  name: string;
  windowMinutes: number;
  maxRequests: number;
  status: 'active' | 'inactive';
  stats: {
    currentRequests: number;
    triggeredCount: number;
  };
}

interface IpLimit {
  id: string;
  type: 'whitelist' | 'blacklist';
  ips: string[];
  description: string;
  status: 'active' | 'inactive';
}

// 仪表盘统计
interface DashboardStats {
  users: {
    total: number;
    active: number;
  };
  requests: {
    today: number;
    total: number;
    successRate: number;
  };
  configs: {
    total: number;
    active: number;
  };
  recentRequests: RequestLog[];
  trendData: ChartDataPoint[];
}
```

### 状态管理设计

```typescript
// store/dashboard.ts
interface DashboardStore {
  stats: DashboardStats | null;
  loading: boolean;
  error: string | null;
  
  // Actions
  fetchStats: () => Promise<void>;
  refreshStats: () => Promise<void>;
  setTimeRange: (range: string) => void;
}

// store/config.ts
interface ConfigStore {
  configs: WebhookConfig[];
  currentConfig: WebhookConfig | null;
  loading: boolean;
  error: string | null;
  
  // Actions
  fetchConfigs: () => Promise<void>;
  createConfig: (data: WebhookConfigFormData) => Promise<void>;
  updateConfig: (id: string, data: WebhookConfigFormData) => Promise<void>;
  deleteConfig: (id: string) => Promise<void>;
  testConfig: (id: string, data: any) => Promise<TestResult>;
}

// store/global.ts
interface GlobalStore {
  theme: 'light' | 'dark';
  language: 'zh' | 'en';
  sidebarCollapsed: boolean;
  isMobile: boolean;
  
  // Actions
  setTheme: (theme: 'light' | 'dark') => void;
  setLanguage: (language: 'zh' | 'en') => void;
  toggleSidebar: () => void;
  setIsMobile: (isMobile: boolean) => void;
}
```

## 错误处理

### 错误类型定义

```typescript
// types/errors.ts
interface ApiError {
  code: string;
  message: string;
  details?: any;
}

interface ValidationError {
  field: string;
  message: string;
}

interface NetworkError {
  type: 'network' | 'timeout' | 'server';
  message: string;
}
```

### 错误处理策略

1. **全局错误边界**: 使用 React Error Boundary 捕获未处理的错误
2. **API 错误处理**: 统一的 API 错误处理和用户友好的错误提示
3. **表单验证**: 使用 Zod 进行表单验证，提供实时反馈
4. **网络错误**: 自动重试机制和离线状态检测

```typescript
// components/common/error-boundary.tsx
class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <ErrorFallback error={this.state.error} />;
    }

    return this.props.children;
  }
}
```

### 网络请求错误处理

```typescript
// lib/api.ts
export async function apiRequest<T>(
  url: string,
  options: RequestInit = {}
): Promise<T> {
  try {
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      throw new ApiError(
        response.status.toString(),
        `HTTP ${response.status}: ${response.statusText}`
      );
    }

    return await response.json();
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new NetworkError('network', '网络请求失败');
  }
}
```

## 测试策略

### 测试层级

1. **单元测试**: 使用 Jest + React Testing Library
   - 工具函数测试
   - 组件单元测试
   - Hook 测试

2. **集成测试**: 
   - 页面级组件测试
   - 状态管理测试
   - API 集成测试

3. **端到端测试**: 使用 Playwright
   - 关键用户流程测试
   - 跨浏览器兼容性测试

### 测试配置

```typescript
// jest.config.js
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/test/setup.ts'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/mock/**',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
};
```

### 测试示例

```typescript
// __tests__/components/dashboard/stats-card.test.tsx
import { render, screen } from '@testing-library/react';
import { StatsCard } from '@/components/dashboard/stats-card';

describe('StatsCard', () => {
  const mockProps = {
    title: '用户数量',
    value: 1250,
    change: { value: 12, type: 'increase' as const },
    icon: <div>icon</div>,
  };

  it('should render stats card with correct data', () => {
    render(<StatsCard {...mockProps} />);
    
    expect(screen.getByText('用户数量')).toBeInTheDocument();
    expect(screen.getByText('1250')).toBeInTheDocument();
    expect(screen.getByText('+12%')).toBeInTheDocument();
  });

  it('should show loading state', () => {
    render(<StatsCard {...mockProps} loading />);
    
    expect(screen.getByTestId('stats-card-skeleton')).toBeInTheDocument();
  });
});
```

## 性能优化

### 代码分割策略

1. **路由级分割**: 每个页面独立打包
2. **组件级分割**: 大型组件懒加载
3. **第三方库分割**: 将大型依赖独立打包

```typescript
// 路由懒加载
const DashboardPage = lazy(() => import('@/app/[locale]/dashboard/page'));
const ConfigPage = lazy(() => import('@/app/[locale]/config/page'));

// 组件懒加载
const TrendChart = lazy(() => import('@/components/dashboard/trend-chart'));
```

### 缓存策略

1. **浏览器缓存**: 静态资源长期缓存
2. **内存缓存**: 常用数据内存缓存
3. **状态持久化**: 关键状态本地存储

### 图片优化

1. **Next.js Image**: 使用 Next.js 优化的 Image 组件
2. **WebP 格式**: 支持现代图片格式
3. **懒加载**: 图片延迟加载

## 响应式设计

### 断点设计

```typescript
// lib/constants.ts
export const BREAKPOINTS = {
  mobile: 768,
  tablet: 1024,
  desktop: 1280,
  wide: 1536,
} as const;
```

### 布局适配

1. **桌面端** (≥1280px): 完整侧边栏 + 主内容区
2. **平板端** (768px-1279px): 可折叠侧边栏
3. **移动端** (<768px): 底部导航 + 全屏内容

### 移动端优化

1. **触摸友好**: 按钮最小 44px 点击区域
2. **手势支持**: 滑动操作支持
3. **性能优化**: 移动端特定的性能优化

## 国际化实现

### i18next 配置

```typescript
// lib/i18n.ts
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import Backend from 'i18next-resources-to-backend';

const resources = (language: string, namespace: string) =>
  import(`/public/locales/${language}/${namespace}.json`);

i18n
  .use(Backend(resources))
  .use(initReactI18next)
  .init({
    lng: 'zh',
    fallbackLng: 'en',
    debug: process.env.NODE_ENV === 'development',
    
    interpolation: {
      escapeValue: false,
    },
    
    react: {
      useSuspense: false,
    },
  });

export default i18n;
```

### 多语言路由

```typescript
// middleware.ts
import { NextRequest, NextResponse } from 'next/server';

const locales = ['zh', 'en'];
const defaultLocale = 'zh';

export function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;
  
  const pathnameIsMissingLocale = locales.every(
    (locale) => !pathname.startsWith(`/${locale}/`) && pathname !== `/${locale}`
  );

  if (pathnameIsMissingLocale) {
    return NextResponse.redirect(
      new URL(`/${defaultLocale}${pathname}`, request.url)
    );
  }
}

export const config = {
  matcher: [
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
```

## 开发工具配置

### ESLint 配置

```json
{
  "extends": [
    "next/core-web-vitals",
    "@typescript-eslint/recommended",
    "prettier"
  ],
  "plugins": ["@typescript-eslint", "react-hooks"],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/no-explicit-any": "warn",
    "react-hooks/rules-of-hooks": "error",
    "react-hooks/exhaustive-deps": "warn"
  }
}
```

### Prettier 配置

```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false
}
```

这个设计文档提供了完整的技术架构和实现方案，确保项目的可维护性、可扩展性和性能表现。所有设计决策都基于现代前端开发的最佳实践。