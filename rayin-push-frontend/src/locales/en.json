{"common": {"welcome": "Welcome to <PERSON><PERSON>", "introduction": "Customizable Webhook message parsing and forwarding system", "description": "Description", "dashboard": "Dashboard", "config": "Interface Config", "channels": "Notification Channels", "logs": "Request Logs", "limits": "Request Limits", "users": "User Management", "settings": "Settings", "language": "Language", "theme": "Theme", "light": "Light", "dark": "Dark", "search": "Search", "create": "Create", "edit": "Edit", "delete": "Delete", "save": "Save", "saving": "Saving...", "cancel": "Cancel", "confirm": "Confirm", "loading": "Loading...", "error": "Error", "success": "Success", "failed": "Failed", "status": "Status", "allStatus": "All Status", "actions": "Actions", "name": "Name", "time": "Time", "type": "Type", "allTypes": "All Types", "enable": "Enable", "disable": "Disable", "enabled": "Enabled", "disabled": "Disabled", "token": "Token", "requestMethod": "Request Method", "management": "Management", "system": "System", "monitoring": "System Monitoring", "analytics": "Analytics", "webhooks": "Webhook Management", "database": "Database Status", "operate": "Operate", "createdTime": "Created Time", "updatedTime": "Updated Time", "totalRecords": "Total {{count}} records", "selectedItems": "{{count}} selected", "batchOperation": "Batch Operation", "batchEnable": "Batch Enable", "batchDisable": "<PERSON>ch Disable", "perPage": "Per page", "itemsPerPage": "items", "firstPage": "First", "lastPage": "Last", "previousPage": "Previous", "nextPage": "Next", "page": "Page {{current}} of {{total}}", "noData": "No data", "auto": "Auto", "showing": "Showing {{from}} to {{to}} of {{total}} entries", "rowsPerPage": "Rows per page"}, "navigation": {"dashboard": "Dashboard", "management": "Management", "config": "Interface Config", "channels": "Notification Channels", "logs": "Request Logs", "limits": "Request Limits", "system": "System", "users": "User Management", "monitoring": "System Monitoring", "analytics": "Analytics", "webhooks": "Webhook Management", "database": "Database Status"}, "dashboard": {"statistics": "Statistics", "totalUsers": "Total Users", "totalRequests": "Total Requests", "successRate": "Success Rate", "todayRequests": "Today's Requests", "recentRequests": "Recent Requests", "requestTrends": "Request Trends", "last24Hours": "Last 24 Hours", "noRecentRequests": "No recent requests", "viewAllLogs": "View All Logs", "refreshData": "Refresh Data", "autoRefresh": "Auto Refresh", "processing": "Processing", "completed": "Completed", "failed": "Failed", "pending": "Pending", "partial": "Partial Success", "success": "Success", "duration": "Duration", "from": "From", "channels": "Channels", "viewDetails": "View Details", "timeRange24h": "24 Hours", "timeRange7d": "7 Days", "timeRange30d": "30 Days"}, "channels": {"title": "Notification Channels", "createChannel": "Create Channel", "editChannel": "Edit Channel", "deleteChannel": "Delete Channel", "channelName": "Channel Name", "channelType": "Channel Type", "channelDescription": "Channel Description", "wechatBot": "WeChat Bot", "feishuBot": "<PERSON><PERSON><PERSON>", "customWebhook": "Custom Webhook", "webhookUrl": "Webhook URL", "httpMethod": "HTTP Method", "requestHeaders": "Request Headers", "requestBody": "Request Body", "urlParameters": "URL Parameters", "builtinTemplate": "Built-in Template", "customTemplate": "Custom Template", "templateVariables": "Template Variables", "messageTemplate": "Message Template", "testChannel": "Test Channel", "testMessage": "Test Message", "sendTest": "Send Test", "testHistory": "Test History", "channelStatus": "Channel Status", "lastTestTime": "Last Test Time", "testSuccess": "Test Success", "testFailed": "Test Failed", "variableHelp": "Variable Help", "supportedVariables": "Supported Variables", "conditionalLogic": "Conditional Logic", "ifElseStatement": "if/else Statement", "arithmeticExpression": "Arithmetic Expression", "noChannelData": "No channel data available", "basicInfo": "Basic Information", "channelConfig": "Channel Configuration", "channelNameRequired": "Channel name is required", "channelTypeRequired": "Please select channel type", "webhookUrlRequired": "Webhook URL is required", "httpMethodRequired": "Please select HTTP method", "messageTemplateRequired": "Message template is required", "channelNamePlaceholder": "Enter channel name", "channelDescPlaceholder": "Enter channel description", "selectChannelTypePlaceholder": "Select channel type", "webhookUrlPlaceholder": "Enter webhook URL", "messageTemplatePlaceholder": "Enter message template", "headerKeyPlaceholder": "Header name", "headerValuePlaceholder": "Header value", "editChannelDesc": "Edit existing notification channel configuration", "createChannelDesc": "Create new notification channel configuration", "builtinTemplateDesc": "Use built-in template with common variable replacements", "variableHelpContent": "Supported variables: ${data} complete data, ${now} current time, ${title} message title, ${content} message content. Conditional statements: ${if condition}...${else}...${/if}. Arithmetic expressions: ${x1 + x2}", "completeData": "Complete data", "currentTime": "Current time", "messageTitle": "Message title", "messageContent": "Message content", "testing": "Testing...", "saving": "Saving...", "searchPlaceholder": "Search channel name or description", "testCompleted": "Test completed"}, "config": {"title": "Interface Config", "createConfig": "Create Config", "createConfigDesc": "Create Interface Config", "editConfig": "Edit Config", "editConfigDesc": "Edit Interface Config", "deleteConfig": "Delete Config", "configName": "Config Name", "configToken": "Config <PERSON>", "configDescription": "Config Description", "requestMethod": "Request Method", "parsingRules": "Parsing Rules", "basicInfo": "Basic Info", "channelSettings": "Channel Settings", "configStatus": "Config Status", "enabled": "Enabled", "disabled": "Disabled", "enableConfig": "Enable Config", "disableConfig": "Disable Config", "searchPlaceholder": "Search config name or description...", "allStatus": "All Status", "noConfigData": "No configuration data", "required": "Required", "configNamePlaceholder": "Enter configuration name", "configDescPlaceholder": "Enter configuration description", "selectMethodPlaceholder": "Select request method", "selectChannelPlaceholder": "Select notification channels", "configNameRequired": "Configuration name is required", "requestMethodRequired": "Request method is required", "channelRequired": "At least one notification channel must be selected", "selectMethodFirst": "Please select request method first", "selectMethodHint": "You need to select a request method before configuring parsing rules", "cancel": "Cancel", "save": "Save", "saving": "Saving...", "newParam": "New Parameter", "variable": "Variable", "messageExtractionRules": "Message Extraction Rules", "contentType": "Content Type", "variableMapping": "Variable Mapping", "addMapping": "Add Mapping", "noParameterMapping": "No parameter mapping, click \"Add Mapping\" to start configuration", "jsonPathPlaceholder": "JSON path (e.g.: user.name, info.address)", "interfaceParamPlaceholder": "Interface parameter name", "templateVariablePlaceholder": "Template variable name", "regexRules": "Regular Expression Rules", "addRule": "Add Rule", "noRegexRules": "No regex rules, click \"Add Rule\" to start configuration", "ruleNumber": "Rule {{number}}", "variableName": "Variable Name", "regexExpression": "Regular Expression", "templateVariableNamePlaceholder": "Variable name used in template", "regexPatternPlaceholder": "e.g.: My name is (.+)", "requestParamPlaceholder": "Request Parameter", "variableNamePlaceholder": "Variable Name", "regexVariableNamePlaceholder": "Variable Name", "regexExpressionPlaceholder": "Regular Expression", "formFieldMapping": "Configure form field mapping, map interface field names to template variable names", "jsonFieldMapping": "Configure JSON field path mapping, support extracting multi-level fields", "regexExtraction": "Use regular expressions to extract data from text, each regex extracts one variable"}, "limits": {"title": "Request Limits", "rateLimits": "Rate Limits", "ipLimits": "IP Limits", "createRule": "Create Rule", "editRule": "Edit Rule", "deleteRule": "Delete Rule", "ruleName": "Rule Name", "ruleDescription": "Rule Description", "timeWindow": "Time Window", "requestLimit": "Request Limit", "ruleStatus": "Rule Status", "ruleType": "Rule Type", "globalLimit": "Global Limit", "perIpLimit": "Per IP Limit", "perUserLimit": "Per User Limit", "seconds": "Seconds", "minutes": "Minutes", "hours": "Hours", "days": "Days", "requestsPerTimeWindow": "Requests per Time Window", "ipAddress": "IP Address", "ipRange": "IP Range", "singleIp": "Single IP", "ipWhitelist": "IP Whitelist", "ipBlacklist": "IP Blacklist", "addIpRule": "Add IP Rule", "removeIpRule": "Remove IP Rule", "currentLimits": "Current Limits", "activeLimits": "Active Limits", "limitStatistics": "Limit Statistics", "triggeredCount": "Triggered Count", "lastTriggered": "Last Triggered", "affectedRequests": "Affected Requests", "exemptIps": "Exempt IPs", "exemptUsers": "Exempt Users", "enableRule": "Enable Rule", "disableRule": "Disable Rule", "ruleHistory": "Rule History", "limitExceeded": "Limit Exceeded", "rateLimitReset": "Rate Limit Reset Time"}, "logs": {"title": "Request Logs", "logDetails": "Log Details", "requestInfo": "Request Info", "responseInfo": "Response Info", "channelResults": "Channel Results", "originalData": "Original Data", "processedData": "Processed Data", "errorInfo": "Error <PERSON>", "timeFilter": "Time Filter", "statusFilter": "Status Filter", "interfaceFilter": "Interface Filter", "allStatus": "All Status", "allInterfaces": "All Interfaces", "requestId": "Request ID", "interfaceName": "Interface Name", "requestTime": "Request Time", "responseTime": "Response Time", "duration": "Duration", "requestStatus": "Request Status", "channelsCount": "Channels Count", "successfulChannels": "Successful Channels", "failedChannels": "Failed Channels", "exportLogs": "Export Logs", "exportCsv": "Export CSV", "exportJson": "Export JSON", "exportSelected": "Export Selected", "exportAll": "Export All", "exportProgress": "Export Progress", "searchLogs": "Search Logs", "searchPlaceholder": "Search request ID, interface name...", "dateRange": "Date Range", "today": "Today", "yesterday": "Yesterday", "last7Days": "Last 7 Days", "last30Days": "Last 30 Days", "customRange": "Custom Range"}, "users": {"title": "User Management", "createUser": "Create User", "editUser": "Edit User", "deleteUser": "Delete User", "userName": "Username", "userEmail": "User Email", "userPassword": "User Password", "confirmPassword": "Confirm Password", "userRole": "User Role", "administrator": "Administrator", "regularUser": "Regular User", "userStatus": "User Status", "active": "Active", "inactive": "Inactive", "suspended": "Suspended", "createdTime": "Created Time", "lastLoginTime": "Last Login Time", "loginCount": "Login <PERSON>", "resetPassword": "Reset Password", "changePassword": "Change Password", "newPassword": "New Password", "currentPassword": "Current Password", "enableUser": "Enable User", "disableUser": "Disable User", "searchUsers": "Search Users", "searchPlaceholder": "Search username, email...", "roleFilter": "Role Filter", "statusFilter": "Status Filter", "allRoles": "All Roles", "allStatus": "All Status", "userOperations": "User Operations", "operationLog": "Operation Log", "operationType": "Operation Type", "operationTime": "Operation Time", "operationDetails": "Operation Details", "loginHistory": "Login History", "loginIp": "Login IP", "loginDevice": "<PERSON><PERSON>", "cannotDeleteAdmin": "Cannot delete admin account", "cannotDeleteSelf": "Cannot delete your own account", "passwordMismatch": "Password mismatch", "userExists": "User already exists", "invalidEmail": "Invalid email format", "passwordTooWeak": "Password is too weak"}}