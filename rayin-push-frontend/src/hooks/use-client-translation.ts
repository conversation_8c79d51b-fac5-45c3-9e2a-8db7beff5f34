'use client'

import { useEffect, useState } from 'react'
import { useLocale } from './use-locale'

export function useClientTranslation(namespace: string = 'common') {
  const { locale } = useLocale()
  const [translations, setTranslations] = useState<Record<string, string>>({})
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const loadTranslations = async () => {
      try {
        setIsLoading(true)
        const translationModule = await import(`@/locales/${locale}.json`)
        const allTranslations = translationModule.default || translationModule
        const translations = allTranslations[namespace] || {}
        setTranslations(translations)
      } catch (error) {
        console.error(`Failed to load translations for ${locale}/${namespace}:`, error)
        setTranslations({})
      } finally {
        setIsLoading(false)
      }
    }

    loadTranslations()
  }, [locale, namespace])

  const t = (key: string, fallback?: string): string => {
    return translations[key] || fallback || key
  }

  return { t, locale, isLoading }
}