'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useTypedTranslation } from '@/hooks/use-typed-translation'
import type { NotificationChannel } from '@/types/data'
import { Save, X, Plus, Trash2, HelpCircle } from 'lucide-react'

interface ChannelEditModalProps {
  channel: NotificationChannel | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onSave: (channel: NotificationChannel) => void
}

interface KeyValuePair {
  key: string
  value: string
}

export function ChannelEditModal({ 
  channel, 
  open, 
  onOpenChange, 
  onSave 
}: ChannelEditModalProps) {
  const { t } = useTypedTranslation('channels')
  const { t: tCommon } = useTypedTranslation('common')
  
  // 表单状态
  const [formData, setFormData] = useState<NotificationChannel>(
    channel || {
      id: '',
      name: '',
      type: 'webhook',
      description: '',
      status: 'active',
      config: {
        url: '',
        method: 'POST',
        headers: {},
        template: ''
      },
      createdTime: '',
      updatedTime: '',
      creator: ''
    }
  )

  // 请求头键值对数组
  const [headers, setHeaders] = useState<KeyValuePair[]>([
    { key: '', value: '' }
  ])

  // 内置模板
  const builtinTemplates = {
    wechat: '**${title}**\n> ${content}\n> 时间: ${now}\n> 数据: ${data}',
    feishu: '**${title}**\n消息: ${content}\n时间: ${now}\n数据: ${data}',
    webhook: '{"message": "${content}", "timestamp": "${now}", "data": ${data}}'
  }

  // 当 channel 变化时更新表单数据
  useEffect(() => {
    if (channel) {
      setFormData(channel)
      // 转换 headers 对象为键值对数组
      const headerPairs = Object.entries(channel.config.headers || {}).map(([key, value]) => ({
        key,
        value
      }))
      setHeaders(headerPairs.length > 0 ? headerPairs : [{ key: '', value: '' }])
    } else {
      // 重置为默认值
      setFormData({
        id: '',
        name: '',
        type: 'webhook',
        description: '',
        status: 'active',
        config: {
          url: '',
          method: 'POST',
          headers: {},
          template: ''
        },
        createdTime: '',
        updatedTime: '',
        creator: ''
      })
      setHeaders([{ key: '', value: '' }])
    }
    setErrors({}) // 清除错误
  }, [channel])

  // 表单验证错误
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [loading, setLoading] = useState(false)

  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = t('channelNameRequired')
    }

    if (!formData.type) {
      newErrors.type = t('channelTypeRequired')
    }

    if (!formData.config.url?.trim()) {
      newErrors.url = t('webhookUrlRequired')
    }

    if (formData.type === 'webhook' && !formData.config.method) {
      newErrors.method = t('httpMethodRequired')
    }

    if (!formData.config.template?.trim()) {
      newErrors.template = t('messageTemplateRequired')
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // 处理表单提交
  const handleSubmit = async () => {
    if (validateForm()) {
      setLoading(true)
      try {
        // 将 headers 数组转换为对象
        const headersObject = headers.reduce((acc, { key, value }) => {
          if (key.trim() && value.trim()) {
            acc[key.trim()] = value.trim()
          }
          return acc
        }, {} as Record<string, string>)

        const updatedChannel = {
          ...formData,
          config: {
            ...formData.config,
            headers: headersObject
          }
        }

        // 模拟API延迟
        await new Promise(resolve => setTimeout(resolve, 1000))
        onSave(updatedChannel)
      } catch (error) {
        console.error('Failed to save channel:', error)
      } finally {
        setLoading(false)
      }
    }
  }

  // 处理渠道类型变化
  const handleTypeChange = (type: 'wechat' | 'feishu' | 'webhook') => {
    setFormData(prev => ({
      ...prev,
      type,
      config: {
        ...prev.config,
        template: builtinTemplates[type],
        method: type === 'webhook' ? prev.config.method || 'POST' : undefined,
        headers: type === 'webhook' ? prev.config.headers : {}
      }
    }))
    
    // 为内置模板类型清除模板错误
    if (type !== 'webhook') {
      const newErrors = { ...errors }
      delete newErrors.template
      setErrors(newErrors)
    }
  }

  // 添加请求头
  const addHeader = () => {
    setHeaders([...headers, { key: '', value: '' }])
  }

  // 删除请求头
  const removeHeader = (index: number) => {
    setHeaders(headers.filter((_, i) => i !== index))
  }

  // 更新请求头
  const updateHeader = (index: number, field: 'key' | 'value', value: string) => {
    const newHeaders = [...headers]
    newHeaders[index][field] = value
    setHeaders(newHeaders)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {channel ? t('editChannel') : t('createChannel')}
          </DialogTitle>
          <DialogDescription>
            {channel ? t('editChannelDesc') : t('createChannelDesc')}
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="basic" className="w-full">
          <TabsList className="grid w-full grid-cols-3 mb-6">
            <TabsTrigger value="basic" className="cursor-pointer">{t('basicInfo')}</TabsTrigger>
            <TabsTrigger value="config" className="cursor-pointer">{t('channelConfig')}</TabsTrigger>
            <TabsTrigger value="template" className="cursor-pointer">{t('messageTemplate')}</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            {/* 基本信息 */}
            <div className="grid grid-cols-2 gap-4">
              {/* 渠道名称 */}
              <div className="space-y-2">
                <Label htmlFor="name">
                  {t('channelName')} <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => {
                    setFormData(prev => ({ ...prev, name: e.target.value }))
                    if (errors.name) {
                      const newErrors = { ...errors }
                      delete newErrors.name
                      setErrors(newErrors)
                    }
                  }}
                  placeholder={t('channelNamePlaceholder')}
                  className={errors.name ? 'border-red-500' : ''}
                />
                {errors.name && (
                  <p className="text-sm text-red-500">{errors.name}</p>
                )}
              </div>

              {/* 渠道类型 */}
              <div className="space-y-2">
                <Label>{t('channelType')} <span className="text-red-500">*</span></Label>
                <Select 
                  value={formData.type} 
                  onValueChange={handleTypeChange}
                >
                  <SelectTrigger className={`w-full cursor-pointer ${errors.type ? 'border-red-500' : ''}`}>
                    <SelectValue placeholder={t('selectChannelTypePlaceholder')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="wechat">{t('wechatBot')}</SelectItem>
                    <SelectItem value="feishu">{t('feishuBot')}</SelectItem>
                    <SelectItem value="webhook">{t('customWebhook')}</SelectItem>
                  </SelectContent>
                </Select>
                {errors.type && (
                  <p className="text-sm text-red-500">{errors.type}</p>
                )}
              </div>
            </div>

            {/* 渠道描述 */}
            <div className="space-y-2">
              <Label htmlFor="description">
                {t('channelDescription')}
              </Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => {
                  setFormData(prev => ({ ...prev, description: e.target.value }))
                }}
                placeholder={t('channelDescPlaceholder')}
                rows={3}
              />
            </div>
          </TabsContent>

          <TabsContent value="config" className="space-y-4">
            {/* Webhook URL */}
            <div className="space-y-2">
              <Label htmlFor="url">
                {t('webhookUrl')} <span className="text-red-500">*</span>
              </Label>
              <Input
                id="url"
                value={formData.config.url || ''}
                onChange={(e) => {
                  setFormData(prev => ({
                    ...prev,
                    config: { ...prev.config, url: e.target.value }
                  }))
                  if (errors.url) {
                    const newErrors = { ...errors }
                    delete newErrors.url
                    setErrors(newErrors)
                  }
                }}
                placeholder={t('webhookUrlPlaceholder')}
                className={errors.url ? 'border-red-500' : ''}
              />
              {errors.url && (
                <p className="text-sm text-red-500">{errors.url}</p>
              )}
            </div>

            {/* HTTP 方法 (仅对 webhook 类型显示) */}
            {formData.type === 'webhook' && (
              <div className="space-y-2">
                <Label>{t('httpMethod')} <span className="text-red-500">*</span></Label>
                <Select 
                  value={formData.config.method || 'POST'} 
                  onValueChange={(value: 'GET' | 'POST') => {
                    setFormData(prev => ({
                      ...prev,
                      config: { ...prev.config, method: value }
                    }))
                    if (errors.method) {
                      const newErrors = { ...errors }
                      delete newErrors.method
                      setErrors(newErrors)
                    }
                  }}
                >
                  <SelectTrigger className={`w-full cursor-pointer ${errors.method ? 'border-red-500' : ''}`}>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="GET">GET</SelectItem>
                    <SelectItem value="POST">POST</SelectItem>
                  </SelectContent>
                </Select>
                {errors.method && (
                  <p className="text-sm text-red-500">{errors.method}</p>
                )}
              </div>
            )}

            {/* 请求头 (仅对 webhook 类型显示) */}
            {formData.type === 'webhook' && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>{t('requestHeaders')}</Label>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addHeader}
                    className="cursor-pointer"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    {tCommon('add')}
                  </Button>
                </div>
                <div className="space-y-2">
                  {headers.map((header, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <Input
                        placeholder={t('headerKeyPlaceholder')}
                        value={header.key}
                        onChange={(e) => updateHeader(index, 'key', e.target.value)}
                        className="flex-1"
                      />
                      <Input
                        placeholder={t('headerValuePlaceholder')}
                        value={header.value}
                        onChange={(e) => updateHeader(index, 'value', e.target.value)}
                        className="flex-1"
                      />
                      {headers.length > 1 && (
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => removeHeader(index)}
                          className="cursor-pointer"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="template" className="space-y-4">
            {/* 模板类型提示 */}
            {formData.type !== 'webhook' && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-base flex items-center gap-2">
                    <Badge variant="outline">{t('builtinTemplate')}</Badge>
                    {formData.type === 'wechat' ? t('wechatBot') : t('feishuBot')}
                  </CardTitle>
                  <CardDescription>
                    {t('builtinTemplateDesc')}
                  </CardDescription>
                </CardHeader>
              </Card>
            )}

            {/* 消息模板 */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="template">
                  {t('messageTemplate')} <span className="text-red-500">*</span>
                </Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    // 显示变量帮助
                    alert(t('variableHelpContent'))
                  }}
                  className="cursor-pointer"
                >
                  <HelpCircle className="h-4 w-4 mr-1" />
                  {t('variableHelp')}
                </Button>
              </div>
              <Textarea
                id="template"
                value={formData.config.template || ''}
                onChange={(e) => {
                  setFormData(prev => ({
                    ...prev,
                    config: { ...prev.config, template: e.target.value }
                  }))
                  if (errors.template) {
                    const newErrors = { ...errors }
                    delete newErrors.template
                    setErrors(newErrors)
                  }
                }}
                placeholder={t('messageTemplatePlaceholder')}
                rows={8}
                className={`font-mono text-sm ${errors.template ? 'border-red-500' : ''}`}
              />
              {errors.template && (
                <p className="text-sm text-red-500">{errors.template}</p>
              )}
            </div>

            {/* 支持的变量说明 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base">{t('supportedVariables')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-sm">
                <div><code className="bg-muted px-1 rounded">${'{data}'}</code> - {t('completeData')}</div>
                <div><code className="bg-muted px-1 rounded">${'{now}'}</code> - {t('currentTime')}</div>
                <div><code className="bg-muted px-1 rounded">${'{title}'}</code> - {t('messageTitle')}</div>
                <div><code className="bg-muted px-1 rounded">${'{content}'}</code> - {t('messageContent')}</div>
                <div className="pt-2 border-t">
                  <p className="font-medium">{t('conditionalLogic')}:</p>
                  <div><code className="bg-muted px-1 rounded">${'{if condition}...{else}...{/if}'}</code></div>
                </div>
                <div className="pt-2 border-t">
                  <p className="font-medium">{t('arithmeticExpression')}:</p>
                  <div><code className="bg-muted px-1 rounded">${'{x1 + x2}'}</code>, <code className="bg-muted px-1 rounded">${'{x1 - x2}'}</code></div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={loading}
            className="cursor-pointer"
          >
            <X className="h-4 w-4 mr-1" />
            {tCommon('cancel')}
          </Button>
          
          <Button
            onClick={handleSubmit}
            disabled={loading}
            className="cursor-pointer"
          >
            <Save className="h-4 w-4 mr-1" />
            {loading ? t('saving') : tCommon('save')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}