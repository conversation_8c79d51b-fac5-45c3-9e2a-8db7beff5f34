'use client'

import { useState } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Power, 
  PowerOff,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight
} from 'lucide-react'
import { useTypedTranslation } from '@/hooks/use-typed-translation'
import { ChannelEditModal } from './channel-form'
import type { NotificationChannel } from '@/types/data'
import { format } from 'date-fns'

interface ChannelListProps {
  channels: NotificationChannel[]
  selectedChannels: string[]
  onSelectionChange: (selected: string[]) => void
  onToggleStatus: (id: string) => void
  onDeleteChannel: (id: string) => void
  onUpdateChannel: (channel: NotificationChannel) => void
  // 分页相关
  currentPage?: number
  pageSize?: number
  total?: number
  onPageChange?: (page: number) => void
  onPageSizeChange?: (pageSize: number) => void
  // 批量操作
  onBatchDelete?: () => void
  onBatchToggleStatus?: (status: 'active' | 'inactive') => void
}

export function ChannelList({
  channels,
  selectedChannels,
  onSelectionChange,
  onToggleStatus,
  onDeleteChannel,
  onUpdateChannel,
  currentPage = 1,
  pageSize = 10,
  total = 0,
  onPageChange,
  onPageSizeChange,
  onBatchDelete,
  onBatchToggleStatus
}: ChannelListProps) {
  const { t } = useTypedTranslation('channels')
  const { t: tCommon } = useTypedTranslation('common')
  
  // 编辑模态框状态
  const [editModalOpen, setEditModalOpen] = useState(false)
  const [editingChannel, setEditingChannel] = useState<NotificationChannel | null>(null)

  // 处理编辑渠道
  const handleEditChannel = (channel: NotificationChannel) => {
    setEditingChannel(channel)
    setEditModalOpen(true)
  }

  // 处理保存渠道
  const handleSaveChannel = (channel: NotificationChannel) => {
    onUpdateChannel(channel)
    setEditModalOpen(false)
    setEditingChannel(null)
  }

  // 处理全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      onSelectionChange(channels.map(channel => channel.id))
    } else {
      onSelectionChange([])
    }
  }

  // 处理单项选择
  const handleSelectItem = (id: string, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedChannels, id])
    } else {
      onSelectionChange(selectedChannels.filter(channelId => channelId !== id))
    }
  }

  // 格式化日期为 yyyy-MM-dd HH:mm:ss
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'yyyy-MM-dd HH:mm:ss')
  }

  // 获取渠道类型显示文本和颜色
  const getTypeDisplay = (type: string) => {
    const typeMap = {
      'wechat': { text: t('wechatBot'), color: 'bg-green-50 text-green-700 border border-green-200 dark:bg-green-900/20 dark:text-green-300 dark:border-green-800' },
      'feishu': { text: t('feishuBot'), color: 'bg-blue-50 text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800' },
      'webhook': { text: t('customWebhook'), color: 'bg-purple-50 text-purple-700 border border-purple-200 dark:bg-purple-900/20 dark:text-purple-300 dark:border-purple-800' }
    }
    return typeMap[type as keyof typeof typeMap] || { text: type, color: 'bg-gray-50 text-gray-700 border border-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600' }
  }

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    return status === 'active' 
      ? 'bg-green-50 text-green-700 border border-green-200 dark:bg-green-900/20 dark:text-green-300 dark:border-green-800' 
      : 'bg-red-50 text-red-700 border border-red-200 dark:bg-red-900/20 dark:text-red-300 dark:border-red-800'
  }

  const isAllSelected = channels.length > 0 && selectedChannels.length === channels.length

  if (channels.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-muted-foreground">
          {t('noChannelData')}
        </div>
      </div>
    )
  }

  return (
    <div className="border rounded-lg overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-8 pl-4">
              <Checkbox
                checked={isAllSelected}
                onCheckedChange={handleSelectAll}
              />
            </TableHead>
            <TableHead className="w-48">{t('channelName')}</TableHead>
            <TableHead className="w-64">{t('channelDescription')}</TableHead>
            <TableHead className="w-32">{t('channelType')}</TableHead>
            <TableHead className="w-20">{t('channelStatus')}</TableHead>
            <TableHead className="w-40">{tCommon('createdTime')}</TableHead>
            <TableHead className="w-40">{tCommon('updatedTime')}</TableHead>
            <TableHead className="w-16">{tCommon('operate')}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {channels.map((channel) => {
            const typeDisplay = getTypeDisplay(channel.type)
            return (
              <TableRow key={channel.id}>
                <TableCell className="w-8 pl-4">
                  <Checkbox
                    checked={selectedChannels.includes(channel.id)}
                    onCheckedChange={(checked) => 
                      handleSelectItem(channel.id, checked as boolean)
                    }
                  />
                </TableCell>
                <TableCell className="font-medium w-48 max-w-48">
                  <div className="truncate" title={channel.name}>
                    {channel.name}
                  </div>
                </TableCell>
                <TableCell className="w-64 max-w-64">
                  <div className="truncate" title={channel.description}>
                    {channel.description}
                  </div>
                </TableCell>
                <TableCell className="w-32">
                  <div className={`inline-flex items-center px-2 py-0.5 rounded-md text-xs font-medium ${typeDisplay.color}`}>
                    {typeDisplay.text}
                  </div>
                </TableCell>
                <TableCell className="w-20">
                  <div className={`inline-flex items-center px-2 py-0.5 rounded-md text-xs font-medium ${getStatusColor(channel.status)}`}>
                    {channel.status === 'active' ? tCommon('active') : tCommon('inactive')}
                  </div>
                </TableCell>
                <TableCell className="w-40">
                  <div className="text-sm text-muted-foreground">
                    {formatDate(channel.createdTime)}
                  </div>
                </TableCell>
                <TableCell className="w-40">
                  <div className="text-sm text-muted-foreground">
                    {formatDate(channel.updatedTime)}
                  </div>
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0 cursor-pointer focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:border-transparent focus:ring-0 focus:ring-offset-0 focus:border-transparent">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem 
                        onClick={() => handleEditChannel(channel)}
                        className="cursor-pointer"
                      >
                        <Edit className="h-4 w-4 mr-2" />
                        {t('editChannel')}
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => onToggleStatus(channel.id)}
                        className="cursor-pointer"
                      >
                        {channel.status === 'active' ? (
                          <>
                            <PowerOff className="h-4 w-4 mr-2" />
                            {tCommon('disable')}
                          </>
                        ) : (
                          <>
                            <Power className="h-4 w-4 mr-2" />
                            {tCommon('enable')}
                          </>
                        )}
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="text-destructive hover:bg-destructive/10 hover:text-destructive focus:bg-destructive/10 focus:text-destructive cursor-pointer"
                        onClick={() => onDeleteChannel(channel.id)}
                      >
                        <Trash2 className="h-4 w-4 mr-2 text-destructive" />
                        {t('deleteChannel')}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            )
          })}
        </TableBody>
      </Table>

      {/* 分页组件 */}
      {onPageChange && total > 0 && (
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 px-6 py-4 border-t bg-gray-50/50 dark:bg-gray-900/50 dark:border-gray-800">
          {/* 左侧：数据统计信息和批量操作 */}
          <div className="flex items-center gap-4 text-sm">
            <span className="text-muted-foreground">{tCommon('totalRecords', { count: total })}</span>
            {selectedChannels.length > 0 && (
              <>
                <span className="text-blue-600 font-medium">
                  {tCommon('selectedItems', { count: selectedChannels.length })}
                </span>
                
                {/* 批量操作按钮 */}
                <div className="flex items-center gap-2">
                  {onBatchToggleStatus && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="sm" className="cursor-pointer h-8">
                          <Power className="h-4 w-4 mr-1" />
                          {tCommon('batchOperation')}
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem 
                          onClick={() => onBatchToggleStatus('active')} 
                          className="cursor-pointer"
                        >
                          <Power className="h-4 w-4 mr-2" />
                          {tCommon('batchEnable')}
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => onBatchToggleStatus('inactive')} 
                          className="cursor-pointer"
                        >
                          <PowerOff className="h-4 w-4 mr-2" />
                          {tCommon('batchDisable')}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                  
                  {onBatchDelete && (
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={onBatchDelete}
                      className="cursor-pointer h-8"
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      {tCommon('delete')}
                    </Button>
                  )}
                </div>
              </>
            )}
          </div>
          
          {/* 右侧：分页控件 */}
          <div className="flex items-center gap-4">
            {/* 每页显示数量 */}
            <div className="flex items-center gap-2 text-sm">
              <span className="text-muted-foreground">{tCommon('perPage')}</span>
              <Select
                value={pageSize.toString()}
                onValueChange={(value) => onPageSizeChange?.(Number(value))}
              >
                <SelectTrigger className="w-20 h-8 text-sm">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
              <span className="text-muted-foreground">{tCommon('itemsPerPage')}</span>
            </div>
            
            {/* 页码信息和导航 */}
            <div className="flex items-center gap-1">
              {/* 最前页按钮 */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPageChange(1)}
                disabled={currentPage <= 1}
                className="h-8 w-8 p-0 cursor-pointer"
              >
                <ChevronsLeft className="h-4 w-4" />
              </Button>
              
              {/* 上一页按钮 */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPageChange(currentPage - 1)}
                disabled={currentPage <= 1}
                className="h-8 w-8 p-0 cursor-pointer"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              
              {/* 页码按钮组 */}
              <div className="flex items-center gap-1 mx-2">
                {(() => {
                  const totalPages = Math.ceil(total / pageSize)
                  const pages = []
                  
                  // 计算显示的页码范围
                  let startPage = Math.max(1, currentPage - 2)
                  let endPage = Math.min(totalPages, currentPage + 2)
                  
                  // 如果当前页靠近开始，显示更多后面的页码
                  if (currentPage <= 3) {
                    endPage = Math.min(totalPages, 5)
                  }
                  
                  // 如果当前页靠近结束，显示更多前面的页码
                  if (currentPage > totalPages - 3) {
                    startPage = Math.max(1, totalPages - 4)
                  }
                  
                  // 第一页
                  if (startPage > 1) {
                    pages.push(
                      <Button
                        key={1}
                        variant="outline"
                        size="sm"
                        onClick={() => onPageChange(1)}
                        className="h-8 w-8 p-0 cursor-pointer"
                      >
                        1
                      </Button>
                    )
                    if (startPage > 2) {
                      pages.push(
                        <span key="ellipsis1" className="px-2 text-muted-foreground">
                          ...
                        </span>
                      )
                    }
                  }
                  
                  // 中间页码
                  for (let i = startPage; i <= endPage; i++) {
                    pages.push(
                      <Button
                        key={i}
                        variant={i === currentPage ? "default" : "outline"}
                        size="sm"
                        onClick={() => onPageChange(i)}
                        className="h-8 w-8 p-0 cursor-pointer"
                      >
                        {i}
                      </Button>
                    )
                  }
                  
                  // 最后一页
                  if (endPage < totalPages) {
                    if (endPage < totalPages - 1) {
                      pages.push(
                        <span key="ellipsis2" className="px-2 text-muted-foreground">
                          ...
                        </span>
                      )
                    }
                    pages.push(
                      <Button
                        key={totalPages}
                        variant="outline"
                        size="sm"
                        onClick={() => onPageChange(totalPages)}
                        className="h-8 w-8 p-0 cursor-pointer"
                      >
                        {totalPages}
                      </Button>
                    )
                  }
                  
                  return pages
                })()}
              </div>
              
              {/* 下一页按钮 */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPageChange(currentPage + 1)}
                disabled={currentPage >= Math.ceil(total / pageSize)}
                className="h-8 w-8 p-0 cursor-pointer"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
              
              {/* 最后页按钮 */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPageChange(Math.ceil(total / pageSize))}
                disabled={currentPage >= Math.ceil(total / pageSize)}
                className="h-8 w-8 p-0 cursor-pointer"
              >
                <ChevronsRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 编辑渠道模态框 */}
      <ChannelEditModal
        channel={editingChannel}
        open={editModalOpen}
        onOpenChange={setEditModalOpen}
        onSave={handleSaveChannel}
      />
    </div>
  )
}